<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro - <PERSON><PERSON>ones</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- AOS - Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="src/css/style.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" id="mainNav">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="src/assets/logo-evo-drones.png" alt="EVO Drones" height="40">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive" 
                    aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
                <i class="bi bi-list"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarResponsive">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html#servicos">Serviços</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.html#sobre">Sobre Nós</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.html#diferenciais">Diferenciais</a></li>
                    <li class="nav-item"><a class="nav-link" href="index.html#contato">Contato</a></li>
                    <li class="nav-item"><a class="nav-link btn btn-primary ms-lg-3" href="login.html">Login</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="page-header bg-dark text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <h1 class="display-4 fw-bold mb-4">Cadastre-se na EVO Drones</h1>
                    <p class="lead mb-5">Crie sua conta para acessar nossos serviços especializados de manutenção e reparo de drones.</p>
                </div>
                <div class="col-lg-6 d-flex justify-content-center" data-aos="fade-left">
                    <div class="drone-animation-sm">
                        <img src="src/assets/drone-3d.png" alt="Drone" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
        <div class="header-particles"></div>
    </header>

    <!-- Formulário de Cadastro -->
    <section class="py-5 py-lg-7 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card border-0 shadow-lg" data-aos="zoom-in">
                        <div class="card-body p-4 p-md-5">
                            <h2 class="fw-bold text-center mb-4">Informações de Cadastro</h2>
                            
                            <form class="needs-validation" novalidate>
                                <!-- Abas de Etapas -->
                                <ul class="nav nav-pills nav-justified mb-5" id="cadastroTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="dados-pessoais-tab" data-bs-toggle="pill" data-bs-target="#dados-pessoais" type="button" role="tab" aria-controls="dados-pessoais" aria-selected="true">
                                            <span class="d-none d-md-block">1. Dados Pessoais</span>
                                            <span class="d-md-none">1</span>
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="dados-drone-tab" data-bs-toggle="pill" data-bs-target="#dados-drone" type="button" role="tab" aria-controls="dados-drone" aria-selected="false">
                                            <span class="d-none d-md-block">2. Dados do Drone</span>
                                            <span class="d-md-none">2</span>
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="confirmacao-tab" data-bs-toggle="pill" data-bs-target="#confirmacao" type="button" role="tab" aria-controls="confirmacao" aria-selected="false">
                                            <span class="d-none d-md-block">3. Confirmação</span>
                                            <span class="d-md-none">3</span>
                                        </button>
                                    </li>
                                </ul>
                                
                                <!-- Conteúdo das Abas -->
                                <div class="tab-content" id="cadastroTabsContent">
                                    <!-- Etapa 1: Dados Pessoais -->
                                    <div class="tab-pane fade show active" id="dados-pessoais" role="tabpanel" aria-labelledby="dados-pessoais-tab">
                                        <div class="row g-4">
                                            <div class="col-md-6">
                                                <label for="nome" class="form-label">Nome Completo</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                                                    <input type="text" class="form-control" id="nome" placeholder="Seu nome completo" required>
                                                    <div class="invalid-feedback">
                                                        Por favor, informe seu nome completo.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="email" class="form-label">Email</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                                    <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                                                    <div class="invalid-feedback">
                                                        Por favor, informe um email válido.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="cpf" class="form-label">CPF</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-card-text"></i></span>
                                                    <input type="text" class="form-control" id="cpf" placeholder="000.000.000-00" required>
                                                    <div class="invalid-feedback">
                                                        Por favor, informe um CPF válido.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="telefone" class="form-label">Telefone</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-phone"></i></span>
                                                    <input type="text" class="form-control" id="telefone" placeholder="(00) 00000-0000" required>
                                                    <div class="invalid-feedback">
                                                        Por favor, informe um telefone válido.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="senha" class="form-label">Senha</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                                    <input type="password" class="form-control" id="senha" placeholder="Crie uma senha segura" required>
                                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <div class="invalid-feedback">
                                                        Por favor, crie uma senha.
                                                    </div>
                                                </div>
                                                <div class="form-text">A senha deve ter pelo menos 8 caracteres, incluindo letras e números.</div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="confirmarSenha" class="form-label">Confirmar Senha</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-lock-fill"></i></span>
                                                    <input type="password" class="form-control" id="confirmarSenha" placeholder="Confirme sua senha" required>
                                                    <button class="btn btn-outline-secondary toggle-password" type="button">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <div class="invalid-feedback">
                                                        As senhas não coincidem.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-12 mt-4">
                                                <button type="button" class="btn btn-primary float-end next-step">
                                                    Próximo <i class="bi bi-arrow-right"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Etapa 2: Dados do Drone -->
                                    <div class="tab-pane fade" id="dados-drone" role="tabpanel" aria-labelledby="dados-drone-tab">
                                        <div class="row g-4">
                                            <div class="col-md-6">
                                                <label for="modeloDrone" class="form-label">Modelo do Drone</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-drone"></i></span>
                                                    <input type="text" class="form-control" id="modeloDrone" placeholder="Ex: DJI Mavic Air 2" required>
                                                    <div class="invalid-feedback">
                                                        Por favor, informe o modelo do drone.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="numeroSerie" class="form-label">Número de Série</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-upc-scan"></i></span>
                                                    <input type="text" class="form-control" id="numeroSerie" placeholder="Número de série do drone" required>
                                                    <div class="invalid-feedback">
                                                        Por favor, informe o número de série.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="dataCompra" class="form-label">Data de Compra</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                                                    <input type="date" class="form-control" id="dataCompra">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="localCompra" class="form-label">Local de Compra</label>
                                                <div class="input-group">
                                                    <span class="input-group-text"><i class="bi bi-shop"></i></span>
                                                    <input type="text" class="form-control" id="localCompra" placeholder="Onde você comprou o drone">
                                                </div>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label for="observacoes" class="form-label">Observações</label>
                                                <textarea class="form-control" id="observacoes" rows="3" placeholder="Informações adicionais sobre o drone (opcional)"></textarea>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label for="fotoDrone" class="form-label">Foto do Drone (opcional)</label>
                                                <input class="form-control" type="file" id="fotoDrone">
                                                <div class="form-text">Formatos aceitos: JPG, PNG. Tamanho máximo: 5MB</div>
                                            </div>
                                            
                                            <div class="col-12 mt-4 d-flex justify-content-between">
                                                <button type="button" class="btn btn-outline-secondary prev-step">
                                                    <i class="bi bi-arrow-left"></i> Anterior
                                                </button>
                                                <button type="button" class="btn btn-primary next-step">
                                                    Próximo <i class="bi bi-arrow-right"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Etapa 3: Confirmação -->
                                    <div class="tab-pane fade" id="confirmacao" role="tabpanel" aria-labelledby="confirmacao-tab">
                                        <div class="text-center mb-4">
                                            <div class="confirmation-icon mb-3">
                                                <i class="bi bi-check-circle-fill"></i>
                                            </div>
                                            <h3 class="fw-bold">Quase lá!</h3>
                                            <p class="text-muted">Por favor, revise suas informações antes de finalizar o cadastro.</p>
                                        </div>
                                        
                                        <div class="row g-4">
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-header">
                                                        <h5 class="mb-0">Dados Pessoais</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <ul class="list-unstyled mb-0">
                                                            <li class="mb-2"><strong>Nome:</strong> <span id="resumoNome"></span></li>
                                                            <li class="mb-2"><strong>Email:</strong> <span id="resumoEmail"></span></li>
                                                            <li class="mb-2"><strong>CPF:</strong> <span id="resumoCPF"></span></li>
                                                            <li class="mb-2"><strong>Telefone:</strong> <span id="resumoTelefone"></span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-header">
                                                        <h5 class="mb-0">Dados do Drone</h5>
                                                    </div>
                                                    <div class="card-body">
                                                        <ul class="list-unstyled mb-0">
                                                            <li class="mb-2"><strong>Modelo:</strong> <span id="resumoModelo"></span></li>
                                                            <li class="mb-2"><strong>Nº de Série:</strong> <span id="resumoSerie"></span></li>
                                                            <li class="mb-2"><strong>Data de Compra:</strong> <span id="resumoData"></span></li>
                                                            <li class="mb-2"><strong>Local de Compra:</strong> <span id="resumoLocal"></span></li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-12">
                                                <div class="form-check mb-4">
                                                    <input class="form-check-input" type="checkbox" id="termos" required>
                                                    <label class="form-check-label" for="termos">
                                                        Eu li e concordo com os <a href="#" class="text-primary">Termos de Uso</a> e <a href="#" class="text-primary">Política de Privacidade</a>.
                                                    </label>
                                                    <div class="invalid-feedback">
                                                        Você deve concordar com os termos para prosseguir.
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="col-12 mt-4 d-flex justify-content-between">
                                                <button type="button" class="btn btn-outline-secondary prev-step">
                                                    <i class="bi bi-arrow-left"></i> Anterior
                                                </button>
                                                <button type="submit" class="btn btn-primary glow-effect">
                                                    <i class="bi bi-check2-circle me-2"></i> Finalizar Cadastro
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <p>Já possui uma conta? <a href="login.html" class="text-primary">Faça login</a></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-5 bg-dark text-white">
        <div class="container">
            <div class="row gy-4">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <img src="src/assets/logo-evo-drones.png" alt="EVO Drones" height="40" class="mb-4">
                    <p class="text-white-75">
                        Especialistas em manutenção e reparo de drones, oferecendo serviços de alta qualidade com técnicos certificados.
                    </p>
                    <div class="social-links mt-4">
                        <a href="#" class="me-3"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="me-3"><i class="bi bi-instagram"></i></a>
                        <a href="#" class="me-3"><i class="bi bi-twitter-x"></i></a>
                        <a href="#" class="me-3"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h5 class="fw-bold mb-4">Links Rápidos</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="index.html" class="text-decoration-none text-white-75">Home</a></li>
                        <li class="mb-2"><a href="index.html#servicos" class="text-decoration-none text-white-75">Serviços</a></li>
                        <li class="mb-2"><a href="index.html#sobre" class="text-decoration-none text-white-75">Sobre Nós</a></li>
                        <li class="mb-2"><a href="index.html#diferenciais" class="text-decoration-none text-white-75">Diferenciais</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h5 class="fw-bold mb-4">Serviços</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-decoration-none text-white-75">Manutenção</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-white-75">Reparo</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-white-75">Upgrade</a></li>
                        <li class="mb-2"><a href="#" class="text-decoration-none text-white-75">Consultoria</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-12">
                    <h5 class="fw-bold mb-4">Contato</h5>
                    <ul class="list-unstyled">
                        <li class="mb-3"><i class="bi bi-geo-alt me-2"></i> Rua Exemplo, 123 - São Paulo, SP</li>
                        <li class="mb-3"><i class="bi bi-telephone me-2"></i> (11) 1234-5678</li>
                        <li class="mb-3"><i class="bi bi-envelope me-2"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 bg-white-50">
            <div class="row">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0 text-white-75">&copy; 2024 EVO Drones. Todos os direitos reservados.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <p class="mb-0 text-white-75">Desenvolvido com <i class="bi bi-heart-fill text-danger"></i></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS - Animate On Scroll -->
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="src/js/main.js"></script>
    
    <script>
        // Inicializa AOS
        AOS.init({
            duration: 800,
            once: true
        });
        
        // Toggle password visibility
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const input = this.previousElementSibling;
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.replace('bi-eye', 'bi-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.replace('bi-eye-slash', 'bi-eye');
                }
            });
        });
        
        // Form validation
        (function () {
            'use strict'
            
            const forms = document.querySelectorAll('.needs-validation');
            
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        })();
        
        // Navegação entre etapas
        document.querySelectorAll('.next-step').forEach(button => {
            button.addEventListener('click', function() {
                // Encontra a aba atual e a próxima
                const currentTab = document.querySelector('.tab-pane.active');
                const currentTabId = currentTab.getAttribute('id');
                const nextTabId = currentTabId === 'dados-pessoais' ? 'dados-drone' : 'confirmacao';
                
                // Atualiza o resumo se estiver indo para a etapa de confirmação
                if (nextTabId === 'confirmacao') {
                    updateSummary();
                }
                
                // Ativa a próxima aba
                const nextTab = new bootstrap.Tab(document.querySelector(`#${nextTabId}-tab`));
                nextTab.show();
            });
        });
        
        document.querySelectorAll('.prev-step').forEach(button => {
            button.addEventListener('click', function() {
                // Encontra a aba atual e a anterior
                const currentTab = document.querySelector('.tab-pane.active');
                const currentTabId = currentTab.getAttribute('id');
                const prevTabId = currentTabId === 'confirmacao' ? 'dados-drone' : 'dados-pessoais';
                
                // Ativa a aba anterior
                const prevTab = new bootstrap.Tab(document.querySelector(`#${prevTabId}-tab`));
                prevTab.show();
            });
        });
        
        // Atualiza o resumo das informações
        function updateSummary() {
            document.getElementById('resumoNome').textContent = document.getElementById('nome').value;
            document.getElementById('resumoEmail').textContent = document.getElementById('email').value;
            document.getElementById('resumoCPF').textContent = document.getElementById('cpf').value;
            document.getElementById('resumoTelefone').textContent = document.getElementById('telefone').value;
            
            document.getElementById('resumoModelo').textContent = document.getElementById('modeloDrone').value;
            document.getElementById('resumoSerie').textContent = document.getElementById('numeroSerie').value;
            document.getElementById('resumoData').textContent = document.getElementById('dataCompra').value;
            document.getElementById('resumoLocal').textContent = document.getElementById('localCompra').value;
        }
    </script>
    
    <style>
        /* Estilos específicos para a página de cadastro */
        .page-header {
            padding: 8rem 0 4rem;
            position: relative;
            background: var(--gradient-tech);
            overflow: hidden;
        }
        
        .header-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        
        .drone-animation-sm {
            width: 250px;
            height: 250px;
            position: relative;
            animation: float 5s ease-in-out infinite;
        }
        
        /* Estilização das abas */
        #cadastroTabs .nav-link {
            color: var(--dark);
            border-radius: 8px;
            padding: 15px;
            transition: var(--transition);
        }
        
        #cadastroTabs .nav-link.active {
            background-color: var(--primary);
            color: white;
            box-shadow: var(--glow);
        }
        
        /* Input groups */
        .input-group-text {
            background-color: transparent;
            border-right: none;
        }
        
        .input-group .form-control {
            border-left: none;
        }
        
        .input-group .form-control:focus {
            box-shadow: none;
        }
        
        .input-group:focus-within {
            box-shadow: 0 0 0 0.25rem rgba(48, 180, 232, 0.25);
            border-radius: 0.375rem;
        }
        
        .input-group:focus-within .input-group-text,
        .input-group:focus-within .form-control {
            border-color: var(--primary);
        }
        
        /* Ícone de confirmação */
        .confirmation-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(48, 180, 232, 0.1);
            border-radius: 50%;
            color: var(--primary);
        }
        
        .confirmation-icon i {
            font-size: 3rem;
        }
    </style>
</body>
</html> 