<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <PERSON><PERSON>ones</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- AOS - Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="src/css/style.css">
</head>
<body class="login-page">
    <div class="container-fluid">
        <div class="row vh-100">
            <!-- Lado Esquerdo - Imagem e Informações -->
            <div class="col-lg-6 d-none d-lg-flex bg-dark text-white p-0">
                <div class="login-banner position-relative w-100">
                    <!-- Overlay com gradiente -->
                    <div class="position-absolute top-0 start-0 w-100 h-100 login-overlay"></div>
                    
                    <!-- Conteúdo do banner -->
                    <div class="position-relative h-100 d-flex flex-column justify-content-between p-5 z-index-1">
                        <!-- Logo no topo -->
                        <div>
                            <a href="index.html" class="d-inline-block mb-5">
                                <img src="src/assets/logo-evo-drones.png" alt="EVO Drones" height="50">
                            </a>
                        </div>
                        
                        <!-- Texto central -->
                        <div data-aos="fade-right" data-aos-delay="300">
                            <h2 class="display-4 fw-bold mb-4">Bem-vindo à plataforma de serviços da EVO Drones</h2>
                            <p class="lead">Acesse sua conta para acompanhar seus serviços, solicitar manutenções e muito mais.</p>
                        </div>
                        
                        <!-- Recursos no rodapé -->
                        <div class="row g-4 mt-auto" data-aos="fade-up" data-aos-delay="500">
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="login-feature-icon me-3">
                                        <i class="bi bi-shield-check"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0">Segurança</h5>
                                        <p class="mb-0 small text-white-75">Dados protegidos</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="login-feature-icon me-3">
                                        <i class="bi bi-speedometer2"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0">Rapidez</h5>
                                        <p class="mb-0 small text-white-75">Serviços ágeis</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="login-feature-icon me-3">
                                        <i class="bi bi-headset"></i>
                                    </div>
                                    <div>
                                        <h5 class="mb-0">Suporte</h5>
                                        <p class="mb-0 small text-white-75">24/7 disponível</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Animação de partículas -->
                    <div class="login-particles"></div>
                    
                    <!-- Drone flutuando -->
                    <div class="login-drone" data-aos="fade-up" data-aos-delay="800">
                        <img src="src/assets/drone-3d.png" alt="Drone" class="img-fluid">
                    </div>
                </div>
            </div>
            
            <!-- Lado Direito - Formulário de Login -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container p-4 p-md-5" data-aos="fade-left">
                    <div class="text-center d-lg-none mb-5">
                        <a href="index.html">
                            <img src="src/assets/logo-evo-drones.png" alt="EVO Drones" height="50" class="mb-4">
                        </a>
                    </div>
                    
                    <h2 class="fw-bold mb-4 text-center">Acesse sua conta</h2>
                    
                    <!-- Abas de Login -->
                    <ul class="nav nav-pills nav-fill mb-4" id="loginTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="cliente-tab" data-bs-toggle="tab" data-bs-target="#cliente" type="button" role="tab" aria-controls="cliente" aria-selected="true">Cliente</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="tecnico-tab" data-bs-toggle="tab" data-bs-target="#tecnico" type="button" role="tab" aria-controls="tecnico" aria-selected="false">Técnico</button>
                        </li>
                    </ul>
                    
                    <!-- Conteúdo das Abas -->
                    <div class="tab-content" id="loginTabsContent">
                        <!-- Aba Cliente -->
                        <div class="tab-pane fade show active" id="cliente" role="tabpanel" aria-labelledby="cliente-tab">
                            <form action="#" method="post" class="needs-validation" novalidate>
                                <div class="mb-4">
                                    <label for="clienteEmail" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                        <input type="email" class="form-control" id="clienteEmail" placeholder="<EMAIL>" required>
                                        <div class="invalid-feedback">
                                            Por favor, informe um email válido.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="clienteSenha" class="form-label">Senha</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                        <input type="password" class="form-control" id="clienteSenha" placeholder="Sua senha" required>
                                        <button class="btn btn-outline-secondary toggle-password" type="button">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <div class="invalid-feedback">
                                            Por favor, informe sua senha.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="clienteLembrar">
                                        <label class="form-check-label" for="clienteLembrar">
                                            Lembrar-me
                                        </label>
                                    </div>
                                    <a href="recuperar-senha.html" class="text-primary">Esqueceu a senha?</a>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 py-3 mb-4 glow-effect">Entrar</button>
                                
                                <div class="text-center">
                                    <p>Não tem uma conta? <a href="cadastro.html" class="text-primary">Cadastre-se</a></p>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Aba Técnico -->
                        <div class="tab-pane fade" id="tecnico" role="tabpanel" aria-labelledby="tecnico-tab">
                            <form action="#" method="post" class="needs-validation" novalidate>
                                <div class="mb-4">
                                    <label for="tecnicoEmail" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                        <input type="email" class="form-control" id="tecnicoEmail" placeholder="<EMAIL>" required>
                                        <div class="invalid-feedback">
                                            Por favor, informe um email válido.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="tecnicoSenha" class="form-label">Senha</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-lock"></i></span>
                                        <input type="password" class="form-control" id="tecnicoSenha" placeholder="Sua senha" required>
                                        <button class="btn btn-outline-secondary toggle-password" type="button">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <div class="invalid-feedback">
                                            Por favor, informe sua senha.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="tecnicoLembrar">
                                        <label class="form-check-label" for="tecnicoLembrar">
                                            Lembrar-me
                                        </label>
                                    </div>
                                    <a href="recuperar-senha.html" class="text-primary">Esqueceu a senha?</a>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100 py-3 mb-4 glow-effect">Entrar</button>
                                
                                <div class="text-center">
                                    <p>Acesso exclusivo para técnicos da EVO Drones.</p>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS - Animate On Scroll -->
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="src/js/main.js"></script>
    
    <script>
        // Inicializa AOS
        AOS.init({
            duration: 800,
            once: true
        });
        
        // Toggle password visibility
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const input = this.previousElementSibling;
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.replace('bi-eye', 'bi-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.replace('bi-eye-slash', 'bi-eye');
                }
            });
        });
        
        // Form validation
        (function () {
            'use strict'
            
            const forms = document.querySelectorAll('.needs-validation');
            
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        })();
        
        // Criar partículas para o login
        document.addEventListener('DOMContentLoaded', function() {
            const loginParticles = document.querySelector('.login-particles');
            if (loginParticles) {
                for (let i = 0; i < 30; i++) {
                    const particle = document.createElement('div');
                    particle.classList.add('login-particle');
                    
                    const size = Math.random() * 4 + 1;
                    const posX = Math.random() * 100;
                    const posY = Math.random() * 100;
                    const delay = Math.random() * 5;
                    const duration = Math.random() * 10 + 10;
                    
                    particle.style.width = `${size}px`;
                    particle.style.height = `${size}px`;
                    particle.style.left = `${posX}%`;
                    particle.style.top = `${posY}%`;
                    particle.style.animationDelay = `${delay}s`;
                    particle.style.animationDuration = `${duration}s`;
                    
                    loginParticles.appendChild(particle);
                }
            }
        });
    </script>
    
    <style>
        /* Estilos específicos para a página de login */
        .login-page {
            background-color: var(--light);
        }
        
        .login-banner {
            height: 100%;
            background: url('src/assets/drone-bg.jpg') center/cover no-repeat;
            overflow: hidden;
        }
        
        .login-overlay {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.85));
            z-index: 0;
        }
        
        .z-index-1 {
            z-index: 1;
        }
        
        .login-form-container {
            max-width: 500px;
            width: 100%;
        }
        
        .login-feature-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(48, 180, 232, 0.2);
            border-radius: 50%;
            color: var(--primary);
        }
        
        .login-feature-icon i {
            font-size: 1.25rem;
        }
        
        .login-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        
        .login-particle {
            position: absolute;
            background-color: var(--primary);
            border-radius: 50%;
            opacity: 0.5;
            animation: floatParticle linear infinite;
        }
        
        .login-drone {
            position: absolute;
            right: 5%;
            bottom: 10%;
            width: 200px;
            height: 200px;
            animation: floatDrone 6s ease-in-out infinite;
            z-index: 1;
        }
        
        @keyframes floatParticle {
            0% {
                transform: translateY(0) translateX(0);
                opacity: 0;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-100vh) translateX(20px);
                opacity: 0;
            }
        }
        
        @keyframes floatDrone {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-30px);
            }
        }
        
        /* Estilização das abas */
        #loginTabs .nav-link {
            color: var(--dark);
            border-radius: 8px;
            padding: 10px 20px;
            transition: var(--transition);
        }
        
        #loginTabs .nav-link.active {
            background-color: var(--primary);
            color: white;
            box-shadow: var(--glow);
        }
        
        /* Input groups */
        .input-group-text {
            background-color: transparent;
            border-right: none;
        }
        
        .input-group .form-control {
            border-left: none;
        }
        
        .input-group .form-control:focus {
            box-shadow: none;
        }
        
        .input-group:focus-within {
            box-shadow: 0 0 0 0.25rem rgba(48, 180, 232, 0.25);
            border-radius: 0.375rem;
        }
        
        .input-group:focus-within .input-group-text,
        .input-group:focus-within .form-control {
            border-color: var(--primary);
        }
        
        /* Responsividade */
        @media (max-width: 992px) {
            .login-form-container {
                padding: 2rem !important;
            }
        }
    </style>
</body>
</html> 