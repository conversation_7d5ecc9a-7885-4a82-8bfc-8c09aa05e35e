<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro - EVO DRONES</title>
    <meta name="description" content="Crie sua conta na plataforma EVO DRONES e tenha acesso aos melhores serviços de manutenção de drones.">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../../assets/images/favicon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- AOS - Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <!-- CSS Principal -->
    <link rel="stylesheet" href="../../../assets/css/main.css">

    <style>
        .register-container {
            min-height: 100vh;
            background: var(--evo-gradient-tech);
            padding: 2rem 0;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(48, 180, 232, 0.1);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 1rem;
            position: relative;
            transition: var(--evo-transition);
        }

        .step.active {
            background: var(--evo-gradient-primary);
            color: white;
            box-shadow: var(--evo-glow);
        }

        .step.completed {
            background: var(--evo-primary);
            color: white;
        }

        .step.inactive {
            background: #e2e8f0;
            color: #64748b;
        }

        .step-connector {
            width: 60px;
            height: 2px;
            background: #e2e8f0;
            transition: var(--evo-transition);
        }

        .step-connector.active {
            background: var(--evo-primary);
        }

        .form-floating .form-control {
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            transition: var(--evo-transition);
        }

        .form-floating .form-control:focus {
            border-color: var(--evo-primary);
            box-shadow: 0 0 0 0.25rem rgba(48, 180, 232, 0.15);
        }

        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.5rem;
            transition: var(--evo-transition);
        }

        .strength-weak { background: #ef4444; }
        .strength-medium { background: #f59e0b; }
        .strength-strong { background: #10b981; }

        .benefits-list {
            background: var(--evo-gradient-primary);
            border-radius: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .benefits-list::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        }

        .benefit-item {
            padding: 1rem;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 1rem;
            transition: var(--evo-transition);
        }

        .benefit-item:hover {
            transform: translateX(10px);
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="register-container d-flex align-items-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-lg-10 col-xl-9">
                    <div class="register-card p-0 overflow-hidden" data-aos="zoom-in" data-aos-duration="800">
                        <div class="row g-0">
                            <!-- Formulário de Cadastro -->
                            <div class="col-lg-7">
                                <div class="p-5">
                                    <!-- Header -->
                                    <div class="text-center mb-4">
                                        <a href="../../public/home/" class="text-decoration-none">
                                            <span class="fw-bold fs-3 text-evo-primary">EVO</span>
                                            <span class="fw-light fs-3 text-evo-dark">DRONES</span>
                                        </a>
                                        <h1 class="h3 fw-bold mt-3 mb-2 text-evo-dark">Crie sua conta</h1>
                                        <p class="text-muted">Junte-se à comunidade EVO DRONES</p>
                                    </div>

                                    <!-- Indicador de Passos -->
                                    <div class="step-indicator">
                                        <div class="step active" id="step1">1</div>
                                        <div class="step-connector" id="connector1"></div>
                                        <div class="step inactive" id="step2">2</div>
                                        <div class="step-connector" id="connector2"></div>
                                        <div class="step inactive" id="step3">3</div>
                                    </div>

                                    <!-- Formulário Multi-Step -->
                                    <form id="registerForm" class="needs-validation" novalidate>
                                        <!-- Passo 1: Dados Pessoais -->
                                        <div class="step-content" id="stepContent1">
                                            <h4 class="fw-semibold mb-4 text-evo-dark">Dados Pessoais</h4>

                                            <div class="row g-3">
                                                <div class="col-12">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="nome" placeholder="Nome completo" required>
                                                        <label for="nome"><i class="bi bi-person me-2"></i>Nome Completo</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe seu nome completo.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                                                        <label for="email"><i class="bi bi-envelope me-2"></i>Email</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe um email válido.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="tel" class="form-control" id="telefone" placeholder="(11) 99999-9999" required>
                                                        <label for="telefone"><i class="bi bi-telephone me-2"></i>Telefone</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe seu telefone.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="cpf" placeholder="000.000.000-00" required>
                                                        <label for="cpf"><i class="bi bi-card-text me-2"></i>CPF</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe um CPF válido.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="date" class="form-control" id="nascimento" required>
                                                        <label for="nascimento"><i class="bi bi-calendar me-2"></i>Data de Nascimento</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe sua data de nascimento.
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-flex justify-content-end mt-4">
                                                <button type="button" class="btn btn-evo-primary px-4" onclick="nextStep(2)">
                                                    Próximo <i class="bi bi-arrow-right ms-2"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Passo 2: Endereço -->
                                        <div class="step-content d-none" id="stepContent2">
                                            <h4 class="fw-semibold mb-4 text-evo-dark">Endereço</h4>

                                            <div class="row g-3">
                                                <div class="col-md-4">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="cep" placeholder="00000-000" required>
                                                        <label for="cep"><i class="bi bi-geo-alt me-2"></i>CEP</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe o CEP.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-8">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="endereco" placeholder="Rua, Avenida..." required>
                                                        <label for="endereco"><i class="bi bi-house me-2"></i>Endereço</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe o endereço.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-3">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="numero" placeholder="123" required>
                                                        <label for="numero">Número</label>
                                                        <div class="invalid-feedback">
                                                            Informe o número.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-5">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="complemento" placeholder="Apto, Bloco...">
                                                        <label for="complemento">Complemento</label>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="bairro" placeholder="Bairro" required>
                                                        <label for="bairro">Bairro</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe o bairro.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <input type="text" class="form-control" id="cidade" placeholder="Cidade" required>
                                                        <label for="cidade">Cidade</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, informe a cidade.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="form-floating">
                                                        <select class="form-select" id="estado" required>
                                                            <option value="">Selecione...</option>
                                                            <option value="AC">Acre</option>
                                                            <option value="AL">Alagoas</option>
                                                            <option value="AP">Amapá</option>
                                                            <option value="AM">Amazonas</option>
                                                            <option value="BA">Bahia</option>
                                                            <option value="CE">Ceará</option>
                                                            <option value="DF">Distrito Federal</option>
                                                            <option value="ES">Espírito Santo</option>
                                                            <option value="GO">Goiás</option>
                                                            <option value="MA">Maranhão</option>
                                                            <option value="MT">Mato Grosso</option>
                                                            <option value="MS">Mato Grosso do Sul</option>
                                                            <option value="MG">Minas Gerais</option>
                                                            <option value="PA">Pará</option>
                                                            <option value="PB">Paraíba</option>
                                                            <option value="PR">Paraná</option>
                                                            <option value="PE">Pernambuco</option>
                                                            <option value="PI">Piauí</option>
                                                            <option value="RJ">Rio de Janeiro</option>
                                                            <option value="RN">Rio Grande do Norte</option>
                                                            <option value="RS">Rio Grande do Sul</option>
                                                            <option value="RO">Rondônia</option>
                                                            <option value="RR">Roraima</option>
                                                            <option value="SC">Santa Catarina</option>
                                                            <option value="SP">São Paulo</option>
                                                            <option value="SE">Sergipe</option>
                                                            <option value="TO">Tocantins</option>
                                                        </select>
                                                        <label for="estado">Estado</label>
                                                        <div class="invalid-feedback">
                                                            Por favor, selecione o estado.
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-flex justify-content-between mt-4">
                                                <button type="button" class="btn btn-outline-secondary px-4" onclick="prevStep(1)">
                                                    <i class="bi bi-arrow-left me-2"></i> Anterior
                                                </button>
                                                <button type="button" class="btn btn-evo-primary px-4" onclick="nextStep(3)">
                                                    Próximo <i class="bi bi-arrow-right ms-2"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Passo 3: Senha e Confirmação -->
                                        <div class="step-content d-none" id="stepContent3">
                                            <h4 class="fw-semibold mb-4 text-evo-dark">Segurança</h4>

                                            <div class="row g-3">
                                                <div class="col-12">
                                                    <div class="form-floating">
                                                        <input type="password" class="form-control" id="senha" placeholder="Senha" required>
                                                        <label for="senha"><i class="bi bi-lock me-2"></i>Senha</label>
                                                        <div class="password-strength" id="passwordStrength"></div>
                                                        <div class="invalid-feedback">
                                                            A senha deve ter pelo menos 8 caracteres.
                                                        </div>
                                                    </div>
                                                    <small class="text-muted">
                                                        A senha deve conter pelo menos 8 caracteres, incluindo letras e números.
                                                    </small>
                                                </div>

                                                <div class="col-12">
                                                    <div class="form-floating">
                                                        <input type="password" class="form-control" id="confirmarSenha" placeholder="Confirmar senha" required>
                                                        <label for="confirmarSenha"><i class="bi bi-lock-fill me-2"></i>Confirmar Senha</label>
                                                        <div class="invalid-feedback">
                                                            As senhas não coincidem.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="termos" required>
                                                        <label class="form-check-label" for="termos">
                                                            Eu concordo com os <a href="#" class="text-evo-primary">Termos de Uso</a> e
                                                            <a href="#" class="text-evo-primary">Política de Privacidade</a>
                                                        </label>
                                                        <div class="invalid-feedback">
                                                            Você deve concordar com os termos.
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="newsletter">
                                                        <label class="form-check-label" for="newsletter">
                                                            Quero receber novidades e promoções por email
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-flex justify-content-between mt-4">
                                                <button type="button" class="btn btn-outline-secondary px-4" onclick="prevStep(2)">
                                                    <i class="bi bi-arrow-left me-2"></i> Anterior
                                                </button>
                                                <button type="submit" class="btn btn-evo-primary px-5 glow-effect">
                                                    <i class="bi bi-check-circle me-2"></i> Criar Conta
                                                </button>
                                            </div>
                                        </div>
                                    </form>

                                    <!-- Link para Login -->
                                    <div class="text-center mt-4">
                                        <p class="mb-0">Já tem uma conta?
                                            <a href="../login/" class="text-evo-primary text-decoration-none fw-semibold">Faça login</a>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Benefícios -->
                            <div class="col-lg-5 d-none d-lg-block">
                                <div class="benefits-list h-100 p-5 d-flex flex-column justify-content-center position-relative">
                                    <div class="position-relative z-3">
                                        <h3 class="fw-bold mb-4">Por que escolher a EVO DRONES?</h3>

                                        <div class="benefit-item">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-shield-check fs-3 me-3"></i>
                                                <div>
                                                    <h5 class="fw-semibold mb-1">Técnicos Certificados</h5>
                                                    <p class="mb-0 opacity-75">Profissionais qualificados e certificados</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="benefit-item">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-lightning-charge fs-3 me-3"></i>
                                                <div>
                                                    <h5 class="fw-semibold mb-1">Serviço Rápido</h5>
                                                    <p class="mb-0 opacity-75">Diagnóstico em até 24 horas</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="benefit-item">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-award fs-3 me-3"></i>
                                                <div>
                                                    <h5 class="fw-semibold mb-1">Garantia Total</h5>
                                                    <p class="mb-0 opacity-75">Garantia em todos os serviços</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="benefit-item">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-headset fs-3 me-3"></i>
                                                <div>
                                                    <h5 class="fw-semibold mb-1">Suporte 24/7</h5>
                                                    <p class="mb-0 opacity-75">Atendimento sempre disponível</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="benefit-item">
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-graph-up fs-3 me-3"></i>
                                                <div>
                                                    <h5 class="fw-semibold mb-1">Acompanhamento Online</h5>
                                                    <p class="mb-0 opacity-75">Monitore seus serviços em tempo real</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <script src="../../../assets/js/main.js"></script>

    <script>
        // Inicializar AOS
        AOS.init({
            duration: 600,
            once: true
        });

        let currentStep = 1;

        function nextStep(step) {
            if (validateCurrentStep()) {
                showStep(step);
            }
        }

        function prevStep(step) {
            showStep(step);
        }

        function showStep(step) {
            // Esconder todos os conteúdos
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.add('d-none');
            });

            // Mostrar conteúdo atual
            document.getElementById(`stepContent${step}`).classList.remove('d-none');

            // Atualizar indicadores
            updateStepIndicators(step);

            currentStep = step;
        }

        function updateStepIndicators(activeStep) {
            for (let i = 1; i <= 3; i++) {
                const stepEl = document.getElementById(`step${i}`);
                const connectorEl = document.getElementById(`connector${i}`);

                if (i < activeStep) {
                    stepEl.className = 'step completed';
                    if (connectorEl) connectorEl.className = 'step-connector active';
                } else if (i === activeStep) {
                    stepEl.className = 'step active';
                    if (connectorEl) connectorEl.className = 'step-connector';
                } else {
                    stepEl.className = 'step inactive';
                    if (connectorEl) connectorEl.className = 'step-connector';
                }
            }
        }

        function validateCurrentStep() {
            const currentContent = document.getElementById(`stepContent${currentStep}`);
            const inputs = currentContent.querySelectorAll('input[required], select[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.checkValidity()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Validação de força da senha
        document.getElementById('senha').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');

            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            strengthBar.className = 'password-strength ';
            if (strength <= 1) {
                strengthBar.classList.add('strength-weak');
            } else if (strength <= 2) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        });

        // Validação de confirmação de senha
        document.getElementById('confirmarSenha').addEventListener('input', function() {
            const senha = document.getElementById('senha').value;
            const confirmar = this.value;

            if (senha !== confirmar) {
                this.setCustomValidity('As senhas não coincidem');
            } else {
                this.setCustomValidity('');
            }
        });

        // Máscara para telefone
        document.getElementById('telefone').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            value = value.replace(/(\d{2})(\d)/, '($1) $2');
            value = value.replace(/(\d{5})(\d)/, '$1-$2');
            this.value = value;
        });

        // Máscara para CPF
        document.getElementById('cpf').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            this.value = value;
        });

        // Máscara para CEP
        document.getElementById('cep').addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            value = value.replace(/(\d{5})(\d)/, '$1-$2');
            this.value = value;
        });

        // Buscar endereço por CEP
        document.getElementById('cep').addEventListener('blur', function() {
            const cep = this.value.replace(/\D/g, '');
            if (cep.length === 8) {
                fetch(`https://viacep.com.br/ws/${cep}/json/`)
                    .then(response => response.json())
                    .then(data => {
                        if (!data.erro) {
                            document.getElementById('endereco').value = data.logradouro;
                            document.getElementById('bairro').value = data.bairro;
                            document.getElementById('cidade').value = data.localidade;
                            document.getElementById('estado').value = data.uf;
                        }
                    })
                    .catch(error => console.error('Erro ao buscar CEP:', error));
            }
        });

        // Submissão do formulário
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            if (!this.checkValidity()) {
                e.stopPropagation();
                this.classList.add('was-validated');
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Loading state
            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;

            try {
                // Simular chamada API
                await new Promise(resolve => setTimeout(resolve, 3000));

                EvoDrones.showToast('Conta criada com sucesso! Redirecionando...', 'success');

                setTimeout(() => {
                    window.location.href = '../login/';
                }, 2000);

            } catch (error) {
                EvoDrones.showToast('Erro ao criar conta. Tente novamente.', 'danger');
            } finally {
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
    </script>
</body>
</html>
