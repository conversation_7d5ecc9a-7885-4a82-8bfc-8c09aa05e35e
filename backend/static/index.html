<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EVO DRONES - Redirecionando...</title>
    <meta http-equiv="refresh" content="0;url=pages/public/home/">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- CSS Principal -->
    <link rel="stylesheet" href="assets/css/main.css">

    <style>
        .redirect-container {
            min-height: 100vh;
            background: var(--evo-gradient-tech);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loader-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(48, 180, 232, 0.1);
        }

        .evo-loader {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(48, 180, 232, 0.1);
            border-top: 4px solid var(--evo-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .logo-text {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .redirect-text {
            color: var(--evo-gray);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .progress-bar-custom {
            height: 4px;
            background: rgba(48, 180, 232, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 1.5rem;
        }

        .progress-fill {
            height: 100%;
            background: var(--evo-gradient-primary);
            width: 0%;
            animation: fillProgress 2s ease-in-out forwards;
        }

        @keyframes fillProgress {
            0% { width: 0%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="loader-card">
            <!-- Logo -->
            <div class="logo-text">
                <span class="text-evo-primary">EVO</span>
                <span class="text-evo-dark">DRONES</span>
            </div>

            <!-- Loader -->
            <div class="evo-loader"></div>

            <!-- Texto -->
            <p class="redirect-text">Carregando plataforma...</p>

            <!-- Barra de Progresso -->
            <div class="progress-bar-custom">
                <div class="progress-fill"></div>
            </div>
        </div>
    </div>

    <script>
        // Redirecionar após 2 segundos
        setTimeout(() => {
            window.location.href = 'pages/public/home/';
        }, 2000);
    </script>
</body>
</html>