package main

import (
	"log"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/compress"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/root/evodrones/backend/config"
	"github.com/root/evodrones/backend/controllers"
	"github.com/root/evodrones/backend/db"
	"github.com/root/evodrones/backend/repositories"
	"github.com/root/evodrones/backend/routes"
	"github.com/root/evodrones/backend/services"
)

func main() {
	// Carregar configuração
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Erro ao carregar configuração: %v", err)
	}

	// Inicializar banco de dados
	if err := db.InitDB(&cfg.Database); err != nil {
		log.Fatalf("Erro ao inicializar banco de dados: %v", err)
	}
	defer db.Close()

	// Criar instância do Fiber
	app := fiber.New(fiber.Config{
		AppName:      "EvoDrones API",
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
	})

	// Middleware
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(compress.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins: "*",
		AllowMethods: "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders: "Origin,Content-Type,Accept,Authorization",
	}))

	// Servir arquivos estáticos do frontend
	app.Static("/assets", "./static/assets")
	app.Static("/", "./static")

	// Rotas específicas para páginas principais
	app.Get("/", func(c *fiber.Ctx) error {
		return c.SendFile("./static/index.html")
	})

	app.Get("/home", func(c *fiber.Ctx) error {
		return c.SendFile("./static/pages/public/home/<USER>")
	})

	app.Get("/login", func(c *fiber.Ctx) error {
		return c.SendFile("./static/pages/auth/login/index.html")
	})

	app.Get("/register", func(c *fiber.Ctx) error {
		return c.SendFile("./static/pages/auth/register/index.html")
	})

	// Rotas dos dashboards
	app.Get("/dashboard/client", func(c *fiber.Ctx) error {
		return c.SendFile("./static/pages/dashboard/client/index.html")
	})

	app.Get("/dashboard/technician", func(c *fiber.Ctx) error {
		return c.SendFile("./static/pages/dashboard/technician/index.html")
	})

	// Fallback para outras páginas
	app.Get("/pages/*", func(c *fiber.Ctx) error {
		path := c.Params("*")
		return c.SendFile("./static/pages/" + path)
	})

	// Rota de health check
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":  "ok",
			"message": "API está funcionando",
		})
	})

	// Repositórios
	dbInstance := db.GetDB()
	droneRepo := repositories.NewDroneRepository(dbInstance)
	userRepo := repositories.NewUserRepository(dbInstance)
	maintenanceRepo := repositories.NewMaintenanceOrderRepository(dbInstance.DB)
	reportRepo := repositories.NewTechnicalReportRepository(dbInstance.DB)

	// Serviços
	authService := services.NewAuthService(userRepo, cfg)
	droneService := services.NewDroneService(droneRepo, userRepo)
	maintenanceService := services.NewMaintenanceOrderService(maintenanceRepo, droneRepo, userRepo)
	reportService := services.NewTechnicalReportService(reportRepo, maintenanceRepo, userRepo)

	// Controladores
	authController := controllers.NewAuthController(authService)
	droneController := controllers.NewDroneController(droneService)
	maintenanceController := controllers.NewMaintenanceOrderController(maintenanceService)
	reportController := controllers.NewTechnicalReportController(reportService)

	// Rotas
	routes.SetupAuthRoutes(app, authController)
	routes.SetupRoutes(app, cfg, droneController, maintenanceController)
	routes.SetupTechnicalReportRoutes(app, cfg, reportController)

	// Iniciar servidor
	log.Printf("Servidor iniciando na porta %s", cfg.Server.Port)
	if err := app.Listen(":" + cfg.Server.Port); err != nil {
		log.Fatalf("Erro ao iniciar servidor: %v", err)
	}
}
